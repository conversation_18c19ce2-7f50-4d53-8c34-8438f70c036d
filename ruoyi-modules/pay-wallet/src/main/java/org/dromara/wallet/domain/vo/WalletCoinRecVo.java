package org.dromara.wallet.domain.vo;

import cn.idev.excel.annotation.ExcelIgnoreUnannotated;
import cn.idev.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.system.api.domain.vo.RemoteUserVo;
import org.dromara.wallet.domain.WalletCoinRec;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 多链用户钱包代币余额记录视图对象
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = WalletCoinRec.class)
public class WalletCoinRecVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * ID标识
     */
    @ExcelProperty(value = "ID标识")
    private Long id;

    /**
     * 钱包地址
     */
    @ExcelProperty(value = "钱包地址")
    private String walletAddress;

    /**
     * 代币合约地址
     */
    @ExcelProperty(value = "代币合约地址")
    private String tokenAddress;

    /**
     * 代币符号
     */
    @ExcelProperty(value = "代币符号")
    private String tokenSymbol;

    /**
     * 余额
     */
    @ExcelProperty(value = "余额")
    private BigDecimal balance;

    /**
     * 代币小数位数
     */
    @ExcelProperty(value = "代币小数位数")
    private Integer decimals;

    /**
     * 链类型
     */
    @ExcelProperty(value = "链类型")
    private String chainType;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;

    /**
     * 租户Id
     */
    @ExcelProperty(value = "租户Id")
    private String tenantId;

    //-------扩展信息信息------//
    /**
     * 用户信息
     */
    private RemoteUserVo userVo;

    /**
     * 归集钱包地址
     */
    private String collectionAddress;

}
